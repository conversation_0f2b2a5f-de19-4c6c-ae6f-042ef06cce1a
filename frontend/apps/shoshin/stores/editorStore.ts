"use client";

import type { Edge, Node } from "@xyflow/react";
import { addEdge } from "@xyflow/react";
import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

interface HistoryState {
  nodes: Node[];
  edges: Edge[];
  timestamp: number;
}

interface EditorState {
  // Current state
  nodes: Node[];
  edges: Edge[];

  // History
  undoStack: HistoryState[];
  redoStack: HistoryState[];

  // Selection
  selectedNodes: Node[];
  selectedEdges: Edge[];

  // Clipboard
  clipboardData: {
    nodes: Node[];
    edges: Edge[];
    timestamp: number;
  } | null;

  // Actions
  setNodes: (nodes: Node[]) => void;
  setEdges: (edges: Edge[]) => void;
  updateSelection: (nodes: Node[], edges: Edge[]) => void;

  // History actions
  saveState: () => void;
  undo: () => boolean;
  redo: () => boolean;
  canUndo: () => boolean;
  canRedo: () => boolean;

  // Clipboard actions
  copy: () => void;
  cut: () => void;
  paste: (position?: { x: number; y: number }) => void;

  // Node/Edge operations
  addNode: (node: Node) => void;
  deleteSelected: () => void;
  deleteEdge: (edgeId: string) => void;
  connectNodes: (connection: any) => void;
}

const MAX_HISTORY_SIZE = 50;

const initialNodes: Node[] = [
  {
    id: "start-1",
    type: "custom",
    position: { x: 250, y: 100 },
    data: {
      label: "Start",
      type: "start",
      description: "Start Workflow",
    },
  },
  {
    id: "llm-1",
    type: "custom",
    position: { x: 400, y: 200 },
    data: {
      label: "LLM",
      type: "agent",
      description: "Language Model",
    },
  },
  {
    id: "knowledge-1",
    type: "custom",
    position: { x: 600, y: 150 },
    data: {
      label: "Knowledge",
      type: "knowledge",
      description: "Vector Search",
    },
  },
  {
    id: "response-1",
    type: "custom",
    position: { x: 800, y: 100 },
    data: {
      label: "Response",
      type: "response",
      description: "API Response",
    },
  },
  {
    id: "condition-1",
    type: "custom",
    position: { x: 500, y: 300 },
    data: {
      label: "Condition",
      type: "condition",
      description: "Decision Point",
    },
  },
  {
    id: "function-1",
    type: "custom",
    position: { x: 700, y: 250 },
    data: {
      label: "Function",
      type: "function",
      description: "Process Data",
    },
  },
];

const initialEdges: Edge[] = [
  {
    id: "e1-2",
    source: "start-1",
    target: "llm-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "e2-3",
    source: "llm-1",
    target: "knowledge-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "e3-4",
    source: "knowledge-1",
    target: "response-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "start-condition",
    source: "start-1",
    target: "condition-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "condition-function",
    source: "condition-1",
    target: "function-1",
    type: "shoshin",
    animated: true,
  },
  {
    id: "function-response",
    source: "function-1",
    target: "response-1",
    type: "shoshin",
    animated: true,
  },
];

const generateUniqueId = (prefix: string): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const useEditorStore = create<EditorState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    nodes: initialNodes,
    edges: initialEdges,
    undoStack: [],
    redoStack: [],
    selectedNodes: [],
    selectedEdges: [],
    clipboardData: null,

    // Basic setters
    setNodes: (nodes) => set({ nodes }),
    setEdges: (edges) => set({ edges }),

    updateSelection: (nodes, edges) => {
      const selectedNodes = nodes.filter((node) => node.selected);
      const selectedEdges = edges.filter((edge) => edge.selected);
      set({ selectedNodes, selectedEdges });
    },

    // History management
    saveState: () => {
      const { nodes, edges, undoStack } = get();
      const newState: HistoryState = {
        nodes: JSON.parse(JSON.stringify(nodes)),
        edges: JSON.parse(JSON.stringify(edges)),
        timestamp: Date.now(),
      };

      const newUndoStack = [...undoStack, newState];
      if (newUndoStack.length > MAX_HISTORY_SIZE) {
        newUndoStack.shift();
      }

      set({
        undoStack: newUndoStack,
        redoStack: [], // Clear redo stack when new action is performed
      });
    },

    undo: () => {
      const { undoStack, nodes, edges } = get();
      if (undoStack.length === 0) return false;

      const previousState = undoStack[undoStack.length - 1];
      const newUndoStack = undoStack.slice(0, -1);

      // Save current state to redo stack
      const currentState: HistoryState = {
        nodes: JSON.parse(JSON.stringify(nodes)),
        edges: JSON.parse(JSON.stringify(edges)),
        timestamp: Date.now(),
      };

      set({
        nodes: previousState.nodes,
        edges: previousState.edges,
        undoStack: newUndoStack,
        redoStack: [...get().redoStack, currentState],
      });

      return true;
    },

    redo: () => {
      const { redoStack, nodes, edges } = get();
      if (redoStack.length === 0) return false;

      const nextState = redoStack[redoStack.length - 1];
      const newRedoStack = redoStack.slice(0, -1);

      // Save current state to undo stack
      const currentState: HistoryState = {
        nodes: JSON.parse(JSON.stringify(nodes)),
        edges: JSON.parse(JSON.stringify(edges)),
        timestamp: Date.now(),
      };

      set({
        nodes: nextState.nodes,
        edges: nextState.edges,
        undoStack: [...get().undoStack, currentState],
        redoStack: newRedoStack,
      });

      return true;
    },

    canUndo: () => get().undoStack.length > 0,
    canRedo: () => get().redoStack.length > 0,

    // Clipboard operations
    copy: () => {
      const { selectedNodes, selectedEdges } = get();
      if (selectedNodes.length === 0 && selectedEdges.length === 0) return;

      // Get all edges that connect the selected nodes
      const nodeIds = new Set(selectedNodes.map((node) => node.id));
      const relevantEdges = selectedEdges.filter(
        (edge) => nodeIds.has(edge.source) && nodeIds.has(edge.target),
      );

      set({
        clipboardData: {
          nodes: JSON.parse(JSON.stringify(selectedNodes)),
          edges: JSON.parse(JSON.stringify(relevantEdges)),
          timestamp: Date.now(),
        },
      });
    },

    cut: () => {
      const { copy, deleteSelected } = get();
      copy();
      deleteSelected();
    },

    paste: (position) => {
      const { clipboardData, nodes, edges, saveState } = get();
      if (!clipboardData || clipboardData.nodes.length === 0) return;

      saveState();

      const defaultPosition = position || { x: 100, y: 100 };

      // Find the top-left position of copied nodes to calculate offset
      const minX = Math.min(
        ...clipboardData.nodes.map((node) => node.position.x),
      );
      const minY = Math.min(
        ...clipboardData.nodes.map((node) => node.position.y),
      );

      const offsetX = defaultPosition.x - minX;
      const offsetY = defaultPosition.y - minY;

      // Create mapping from old IDs to new IDs
      const idMapping = new Map<string, string>();

      // Create new nodes with unique IDs and adjusted positions
      const newNodes: Node[] = clipboardData.nodes.map((node) => {
        const newId = generateUniqueId(node.data.type || "node");
        idMapping.set(node.id, newId);

        return {
          ...node,
          id: newId,
          position: {
            x: node.position.x + offsetX,
            y: node.position.y + offsetY,
          },
          selected: false,
        };
      });

      // Create new edges with updated node references
      const newEdges: Edge[] = clipboardData.edges
        .filter(
          (edge) => idMapping.has(edge.source) && idMapping.has(edge.target),
        )
        .map((edge) => ({
          ...edge,
          id: generateUniqueId("edge"),
          source: idMapping.get(edge.source)!,
          target: idMapping.get(edge.target)!,
          selected: false,
        }));

      set({
        nodes: [...nodes, ...newNodes],
        edges: [...edges, ...newEdges],
      });
    },

    // Node/Edge operations
    addNode: (node) => {
      const { saveState, nodes } = get();
      saveState();
      set({ nodes: [...nodes, node] });
    },

    deleteSelected: () => {
      const { selectedNodes, selectedEdges, nodes, edges, saveState } = get();
      if (selectedNodes.length === 0 && selectedEdges.length === 0) return;

      saveState();

      const selectedNodeIds = selectedNodes.map((node) => node.id);
      const selectedEdgeIds = selectedEdges.map((edge) => edge.id);

      // Remove selected nodes and edges
      const newNodes = nodes.filter(
        (node) => !selectedNodeIds.includes(node.id),
      );
      const newEdges = edges.filter(
        (edge) =>
          !selectedEdgeIds.includes(edge.id) &&
          !selectedNodeIds.includes(edge.source) &&
          !selectedNodeIds.includes(edge.target),
      );

      set({
        nodes: newNodes,
        edges: newEdges,
        selectedNodes: [],
        selectedEdges: [],
      });
    },

    deleteEdge: (edgeId) => {
      const { edges, saveState } = get();
      saveState();

      const newEdges = edges.filter((edge) => edge.id !== edgeId);
      set({
        edges: newEdges,
        selectedEdges: [],
      });
    },

    connectNodes: (connection) => {
      const { saveState, edges } = get();
      saveState();
      set({ edges: addEdge(connection, edges) });
    },
  })),
);
